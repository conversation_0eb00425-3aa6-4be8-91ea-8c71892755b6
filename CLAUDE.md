# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a legal knowledge graph Q&A system built as an MVP (學生版、從簡). Users input legal questions in Chinese via a web frontend, and the system uses LLM to generate Cypher queries for Neo4j database, returning simplified answers with knowledge graph visualizations.

## Architecture

### Backend (Python FastAPI)
- **FastAPI application** in `backend/` using uvicorn server
- **Two-step LLM workflow**:
  1. Generate read-only Cypher queries from user questions + Neo4j schema
  2. Generate Chinese answers from query results
- **Neo4j integration** for legal case data queries
- **Security-focused**: Only read operations, automatic LIMIT enforcement, query timeouts

### Frontend (Vue 3 + JavaScript)
- **Vue 3 SPA** with Vite bundler in `frontend/`
- **TailwindCSS** for styling (minimal, academic look)
- **vis-network** for knowledge graph visualization
- **Simple chat interface** with predefined example questions

### Data Analysis
- Legal case data processing scripts in `data-analysis/`
- Cypher query outputs and sample data for testing
- Neo4j schema extraction and validation tools

## Common Development Commands

### Backend Setup and Running
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, OPENAI_API_KEY
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Frontend Setup and Running  
```bash
cd frontend
npm install
npm run dev          # Development server
npm run build        # Production build
npm run preview      # Preview production build
npm run format       # Format code with Prettier
```

### Data Analysis
```bash
cd data-analysis
python -m venv venv
source venv/bin/activate
conda activate p312  # If using conda environment
python verdict_graph.py       # Process legal data
python batch_upload_to_neo4j.py  # Upload to Neo4j
```

## Key Files and Structure

### Backend Core
- `backend/app/main.py` - FastAPI application entry point
- `backend/app/config/settings.py` - Environment configuration with pydantic
- `backend/app/api/routes.py` - Main API router
- `backend/app/api/v1/chat.py` - Chat endpoint handling LLM workflow
- `backend/app/services/` - Core business logic (Neo4j, LLM, schema services)
- `backend/app/utils/` - Security utilities (Cypher validation, schema parsing)

### Frontend Core
- `frontend/src/App.vue` - Main Vue component
- `frontend/src/services/api.js` - Backend API communication
- `frontend/src/router/index.js` - Vue Router configuration

### Configuration
- `backend/.env.example` - Environment variables template
- `backend/schema/schema.json` - Cached Neo4j schema (auto-generated)

## Environment Variables

Required in `backend/.env`:
- `NEO4J_URI`, `NEO4J_USER`, `NEO4J_PASSWORD` - Neo4j connection
- `OPENAI_API_KEY` - OpenAI API access
- `MODEL_NAME` - LLM model (default: gpt-4o-mini)
- `QUERY_LIMIT` - Max query results (default: 100)
- `QUERY_TIMEOUT_MS` - Query timeout (default: 8000ms)
- `ALLOWED_ORIGINS` - CORS origins for frontend

## API Endpoints

- `POST /api/chat` - Main chat endpoint (question → answer + graph)
- `GET /api/examples` - Predefined example questions
- `GET /api/schema` - Neo4j schema information
- `POST /api/schema/refresh` - Refresh cached schema
- `GET /health` - Health check

## Security Features

- **Read-only Cypher**: Blocks CREATE/MERGE/DELETE/SET/DROP operations
- **Automatic LIMIT**: Adds LIMIT clause if missing, enforces maximum
- **Query timeout**: Prevents long-running queries
- **CORS protection**: Restricts to configured origins only
- **No API key exposure**: Keys stored server-side only

## Development Notes

- **Chinese language focus**: All prompts and responses in Traditional Chinese
- **Academic/student project**: Simplified implementation, clear architecture for thesis defense
- **Local/single-machine deployment**: Not designed for production scaling
- **No authentication**: Open access system for demonstration
- **No test framework currently configured**: Manual testing via API docs and frontend

## Data Sources

- Legal case documents from Taiwan courts
- Neo4j knowledge graph with entities: cases, people, companies, laws, relationships
- Pre-processed Cypher queries in `data-analysis/cypher_output/`
- Sample data subset in `data-analysis/sample_500/`

## Common Issues

- **Neo4j connection**: Ensure Neo4j is running and credentials are correct
- **OpenAI API**: Check API key validity and model availability
- **CORS errors**: Verify `ALLOWED_ORIGINS` includes your frontend URL
- **Schema loading**: Delete `backend/schema/schema.json` to force schema refresh
- **Chinese encoding**: Ensure all files use UTF-8 encoding

## Graph Visualization Limits

- Max 100 nodes, 200 edges to prevent browser lag
- Node colors by entity type (cases, people, companies, etc.)
- Simple pan/zoom controls, basic layout algorithms