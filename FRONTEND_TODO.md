# 前端 TODO（Vue 3 + Vite + TailwindCSS + vis-network）

依據 SPEC.md，並基於以下已確認條件：
- 圖譜套件：vis-network
- 前端使用 JavaScript（不使用 TypeScript）

---

## 0) 專案基礎
- [ ] 使用 Vite 建立 Vue 3 專案骨架（不安裝、僅列步驟於 README）
- [ ] Tailwind 基礎設定（不安裝、僅列步驟於 README）
- [ ] 專案結構：src/components, src/views, src/services, src/styles

## 1) 介面與元件
- [ ] AppLayout：簡單 Header（標題＋清空對話按鈕）與主區域分佈
- [ ] ExamplesBar：3–5 個查詢範例按鈕（由 /api/examples 取得）
- [ ] ChatWindow：訊息列表（user/assistant 泡泡）、Loading 提示
- [ ] ChatInput：輸入框（Enter 送出 / Shift+Enter 換行）、送出按鈕
- [ ] GraphPanel（vis-network）：顯示 nodes/edges，工具列（Fit/Reset）
- [ ] Legend：節點顏色分類（依 type）

## 2) 狀態與資料流
- [ ] store（簡易）：messages（[{role, content}]）、graphData（nodes, edges）、loading 狀態
- [ ] examples 由 API 載入；點擊填入 ChatInput 並觸發送出
- [ ] 發送 /api/chat 請求；顯示分段 Loading（生成查詢/查詢資料/產生回答）或合併為單一 Loading 文案
- [ ] 收到回應後：
  - 新增 assistant 訊息（answerText）
  - 將 graphData 傳至 GraphPanel
  - 可選：提供一個「查看 Cypher」的收合區塊（不一定預設展開）

## 3) 視覺與樣式
- [ ] Tailwind 基本樣式：素雅、對齊、空白間距
- [ ] Chat 泡泡：左右對齊、輕微差異背景色
- [ ] 針對小螢幕調整（簡單 RWD）

## 4) Graph（vis-network）實作
- [ ] 初始化 Network（container、data、options）
- [ ] 節點樣式：
      - 根據 type 指派顏色（定義 4–6 種基本色）
      - label 顯示 name 或 type+id 的簡短資訊
- [ ] 邊樣式：顯示 label（關係型別）
- [ ] 互動：拖曳、縮放；Fit/Reset 按鈕
- [ ] 性能：節點≤100、邊≤200 時仍順暢

## 5) 與後端 API 的介接
- [ ] services/api.js：封裝 GET /api/examples、GET /api/schema、POST /api/chat
- [ ] 錯誤處理：
  - 顯示簡短錯誤提示（Toast 或簡單框）
  - 無資料時的空狀態提示

## 6) 文件
- [ ] README（前端）：如何啟動 Vite、如何連接後端 API、環境變數設定（例如 VITE_API_BASE，可選）
- [ ] 範例截圖（可選，簡單即可）

## 7) 測試（簡易）
- [ ] 互動檢查：
  - 範例按鈕→自動填入→送出
  - Loading 顯示與隱藏
  - 回應渲染（訊息＋圖譜）
- [ ] 邊界情況：無結果提示、不合法查詢錯誤提示

---

## 驗收標準（Minimum Acceptance Criteria）
- [ ] 頁面載入可看到範例按鈕
- [ ] 能輸入問題並送出，看到回答與對應圖譜
- [ ] 圖譜可拖曳、縮放，Fit/Reset 可用
- [ ] 無資料/錯誤有清楚提示

