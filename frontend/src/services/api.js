const BASE = import.meta.env.VITE_API_BASE || 'http://localhost:8000/api'

async function http(method, path, body) {
  const res = await fetch(`${BASE}${path}`, {
    method,
    headers: { 'Content-Type': 'application/json' },
    body: body ? JSON.stringify(body) : undefined,
  })
  if (!res.ok) {
    let detail = ''
    try { const j = await res.json(); detail = j.detail || JSON.stringify(j) } catch {}
    throw new Error(detail || `HTTP ${res.status}`)
  }
  return res.json()
}

export const api = {
  getExamples() { return http('GET', '/examples') },
  getSchema() { return http('GET', '/schema') },
  chat(message, history) { return http('POST', '/chat', { message, history }) },
}

