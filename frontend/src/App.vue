<script setup>
import { ref, onMounted, computed } from 'vue'
import { api } from './services/api'
import { DataSet, Network } from 'vis-network/standalone'

const messages = ref([])
const input = ref('')
const loading = ref(false)
const examples = ref([])
const graphData = ref({ nodes: [], edges: [] })
const selectedNode = ref(null)
const graphLoading = ref(false)

// 簡單的 toast 提示
const toasts = ref([])
function showToast(text) {
  const id = Date.now()
  toasts.value.push({ id, text })
  setTimeout(() => {
    toasts.value = toasts.value.filter((t) => t.id !== id)
  }, 3000)
}

// vis-network 以 CDN/ESM 載入（在 index.html 加載），這裡直接使用全域 vis
let network = null
const graphContainer = ref(null)

onMounted(async () => {
  try {
    examples.value = await api.getExamples()
  } catch (e) {
    console.warn(e)
  }
  initGraph()
})

function initGraph() {
  if (!graphContainer.value) return
  const nodes = new DataSet([])
  const edges = new DataSet([])
  const container = graphContainer.value
  const data = { nodes, edges }
  const options = {
    physics: { stabilization: true },
    nodes: { 
      shape: 'dot', 
      size: 12,
      chosen: false, // 禁用選中狀態的顏色變化
      font: {
        size: 12,
        color: '#000000'
      }
    },
    edges: { arrows: 'to', smooth: { type: 'dynamic' } },
    interaction: { hover: true, tooltipDelay: 200 },
    groups: {} // 清空群組配置，避免群組顏色覆蓋個別節點顏色
  }
  network = new Network(container, data, options)

  // 監聽節點點擊事件
  network.on('click', (params) => {
    if (params.nodes.length > 0) {
      const nodeId = params.nodes[0]
      const nodeData = graphData.value.nodes.find((n) => n.id === nodeId)
      selectedNode.value = nodeData
    } else {
      selectedNode.value = null
    }
  })
}

function colorForType(type, role = null) {
  // 如果是 Person 類型且有角色，使用角色顏色
  if (type === 'Person' && role) {
    return getPersonRoleColor(role)
  }
  
  // 其他類型使用預設顏色
  const map = {
    Case: '#60a5fa',
    Person: '#34d399', // 預設綠色，作為後備
    Organization: '#f59e0b',
    Location: '#f472b6',
    Law: '#22d3ee',
    Event: '#a78bfa',
  }
  return map[type] || '#cccccc' // 返回灰色而不是 undefined
}

// 人員角色顏色映射
function getPersonRoleColor(role) {
  const roleColors = {
    '檢察官': '#dc2626', // 紅色
    '法官': '#7c3aed', // 紫色
    '被告': '#ea580c', // 橙色
    '書記官': '#059669', // 綠色
    '被害人': '#0891b2', // 青色
    '證人': '#4338ca', // 藍色
    '告訴人': '#be123c', // 粉紅色
    '辯護人': '#166534', // 深綠色
    '共犯': '#b45309', // 橙褐色
  }
  return roleColors[role] || '#34d399' // 預設綠色
}

// 計算屬性：節點顏色映射（包含人員角色）
const nodeColors = computed(() => {
  return {
    Case: '#60a5fa',
    Organization: '#f59e0b',
    Location: '#f472b6',
    Law: '#22d3ee',
    Event: '#a78bfa',
    // 人員角色分別顯示
    '檢察官': '#dc2626',
    '法官': '#7c3aed',
    '被告': '#ea580c',
    '書記官': '#059669',
    '被害人': '#0891b2',
    '證人': '#4338ca',
    '告訴人': '#be123c',
    '辯護人': '#166534',
    '共犯': '#b45309',
    '其他人員': '#34d399', // 其他人員角色
  }
})

// 節點類型中文標籤
function getNodeTypeLabel(type) {
  const labels = {
    Case: '案件',
    Organization: '組織',
    Location: '地點',
    Law: '法條',
    Event: '事件',
    // 人員角色直接返回角色名稱
    '檢察官': '檢察官',
    '法官': '法官',
    '被告': '被告',
    '書記官': '書記官',
    '被害人': '被害人',
    '證人': '證人',
    '告訴人': '告訴人',
    '辯護人': '辯護人',
    '共犯': '共犯',
    '其他人員': '其他人員',
  }
  return labels[type] || type
}

// 計算屬性：當前圖譜中出現的節點類型圖例
const activeLegendItems = computed(() => {
  const items = {}
  
  // 遍歷當前圖譜的節點
  if (graphData.value.nodes) {
    for (const node of graphData.value.nodes) {
      if (node.type === 'Person' && node.properties?.role) {
        // 人員節點按角色分類
        const role = node.properties.role
        if (nodeColors.value[role]) {
          items[role] = nodeColors.value[role]
        } else {
          items['其他人員'] = nodeColors.value['其他人員']
        }
      } else {
        // 其他類型節點
        items[node.type] = nodeColors.value[node.type]
      }
    }
  }
  
  return items
})

function renderGraph(g) {
  if (!network || !g) {
    console.warn('renderGraph: network or graph data missing', { network: !!network, g })
    return
  }

  if (!g.nodes || !Array.isArray(g.nodes)) return

  // 開始圖譜渲染loading
  graphLoading.value = true

  const nodes = g.nodes.map((n) => {
    const nodeColor = colorForType(n.type, n.properties?.role)
    console.log(`Node ${n.id} (${n.type}${n.properties?.role ? ', role: ' + n.properties.role : ''}): color = ${nodeColor}`)
    
    // 直接使用後端提供的label（已經根據節點類型處理過）
    const nodeLabel = n.label || String(n.id)
    
    return {
      id: n.id,
      label: nodeLabel,
      color: nodeColor, // 直接使用顏色字符串，移除group屬性
      title: nodeLabel, // 添加title作為tooltip，確保label顯示
      font: { color: '#000000' }, // 確保字體顏色
      // 保存完整的節點資料供點擊時使用
      ...n,
    }
  })
  const edges = (g.edges || []).map((e) => ({
    id: e.id,
    from: e.source,
    to: e.target,
    label: e.label,
  }))

  // 完全銷毀並重新創建網絡
  if (network) {
    network.destroy()
    network = null
  }
  
  setTimeout(() => {
    // 重新初始化網絡
    const nodeDataSet = new DataSet(nodes)
    const edgeDataSet = new DataSet(edges)
    const container = graphContainer.value
    const data = { nodes: nodeDataSet, edges: edgeDataSet }
    const options = {
      physics: { stabilization: true },
      nodes: { 
        shape: 'dot', 
        size: 12,
        chosen: false,
        font: {
          size: 12,
          color: '#000000'
        }
      },
      edges: { arrows: 'to', smooth: { type: 'dynamic' } },
      interaction: { hover: true, tooltipDelay: 200 }
    }
    network = new Network(container, data, options)
    
    // 重新綁定點擊事件
    network.on('click', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0]
        const nodeData = graphData.value.nodes.find((n) => n.id === nodeId)
        selectedNode.value = nodeData
      } else {
        selectedNode.value = null
      }
    })
    
    network.fit({ animation: { duration: 300, easingFunction: 'easeInOutQuad' } })
    
    // 渲染完成後關閉loading
    setTimeout(() => {
      graphLoading.value = false
    }, 500)
  }, 100)
}

async function send() {
  const text = input.value.trim()
  if (!text) return
  loading.value = true
  messages.value.push({ role: 'user', content: text })
  input.value = ''
  try {
    const resp = await api.chat(text, messages.value)
    messages.value.push({
      role: 'assistant',
      content: resp.answerText,
      meta: resp.meta,
      query: resp.query,
    })
    graphData.value = resp.graph
    renderGraph(graphData.value)
  } catch (e) {
    messages.value.push({ role: 'assistant', content: `錯誤：${e.message || e}` })
    showToast(`錯誤：${e.message || e}`)
  } finally {
    loading.value = false
  }
}

function useExample(msg) {
  input.value = msg
  send()
}

function clearAll() {
  messages.value = []
  graphData.value = { nodes: [], edges: [] }
  graphLoading.value = false
  if (network) network.setData({ nodes: [], edges: [] })
}
</script>

<template>
  <div class="h-screen flex flex-col">
    <header class="px-4 py-3 border-b flex items-center justify-between">
      <h1 class="text-lg font-semibold">法律知識圖譜問答（MVP）</h1>
      <button class="text-sm text-gray-600 hover:text-black" @click="clearAll">清空對話</button>
    </header>

    <main class="flex-1 flex gap-4 p-4 h-full">
      <!-- 左側：聊天區（窄） -->
      <section class="w-80 flex flex-col space-y-3">
        <!-- 範例列 -->
        <div class="flex flex-wrap gap-1">
          <button
            v-for="ex in examples"
            :key="ex.id"
            class="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-xs"
            @click="useExample(ex.message)"
          >
            {{ ex.title }}
          </button>
        </div>

        <!-- 訊息列表 -->
        <div class="border rounded p-3 flex-1 overflow-y-auto space-y-2 bg-white">
          <div
            v-for="(m, idx) in messages"
            :key="idx"
            class="flex"
            :class="m.role === 'user' ? 'justify-end' : 'justify-start'"
          >
            <div
              class="max-w-[90%] px-2 py-2 rounded text-sm break-words overflow-hidden"
              :class="m.role === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'"
              style="word-wrap: break-word; overflow-wrap: break-word"
            >
              <p
                class="whitespace-pre-wrap break-words"
                style="word-wrap: break-word; overflow-wrap: break-word"
              >
                {{ m.content }}
              </p>
              <details v-if="m.query" class="mt-1 text-xs text-gray-700">
                <summary>查看 Cypher</summary>
                <pre class="overflow-auto text-xs">{{ m.query.cypher }}</pre>
              </details>
            </div>
          </div>
          <div v-if="loading" class="text-xs text-gray-500">生成查詢/查詢資料/產生回答中…</div>
        </div>

        <!-- 輸入框 -->
        <div class="flex gap-2">
          <textarea
            v-model="input"
            @keydown.enter.exact.prevent="send"
            @keydown.shift.enter="input += '\n'"
            rows="2"
            class="flex-1 border rounded px-2 py-1 text-sm"
            placeholder="請輸入法律問題..."
          />
          <button
            class="px-3 py-1 bg-black text-white rounded text-sm"
            @click="send"
            :disabled="loading"
          >
            送出
          </button>
        </div>
      </section>

      <!-- 中間：圖譜區（最大） -->
      <section class="flex-1 flex flex-col space-y-2">
        <div class="flex items-center justify-between">
          <h2 class="font-medium">圖譜</h2>
          <div class="flex items-center space-x-4">
            <!-- 圖例 -->
            <div class="flex items-center space-x-3 text-xs">
              <div
                v-for="(color, type) in activeLegendItems"
                :key="type"
                class="flex items-center space-x-1"
              >
                <div
                  class="w-3 h-3 rounded-full"
                  :style="{ backgroundColor: color }"
                ></div>
                <span class="text-gray-600">{{ getNodeTypeLabel(type) }}</span>
              </div>
            </div>
            <!-- 控制按鈕 -->
            <div class="space-x-2 text-sm">
              <button class="px-2 py-1 border rounded" @click="network && network.fit()">Fit</button>
              <button
                class="px-2 py-1 border rounded"
                @click="
                  network &&
                  network.setData({ nodes: network.body.data.nodes, edges: network.body.data.edges })
                "
              >
                Reset
              </button>
            </div>
          </div>
        </div>
        <div class="relative border rounded flex-1 bg-white">
          <div ref="graphContainer" class="w-full h-full"></div>
          <!-- 圖譜loading遮罩 -->
          <div 
            v-if="graphLoading" 
            class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center"
          >
            <div class="flex flex-col items-center space-y-3">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <div class="text-sm text-gray-600">圖譜渲染中...</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 右側：屬性面板（窄） -->
      <section class="w-80 flex flex-col">
        <h2 class="font-medium mb-2">節點屬性</h2>
        <div v-if="selectedNode" class="border rounded p-3 bg-gray-50 flex-1 overflow-y-auto">
          <div class="flex justify-between items-center mb-3">
            <h3 class="font-medium text-sm">{{ selectedNode.type }}</h3>
            <button @click="selectedNode = null" class="text-gray-500 hover:text-black">✕</button>
          </div>
          <div class="text-sm font-medium mb-2 text-blue-600">{{ selectedNode.label }}</div>
          <div class="space-y-2">
            <div
              v-for="(value, key) in selectedNode.properties"
              :key="key"
              class="border-b border-gray-200 pb-1"
            >
              <div class="font-medium text-gray-600 text-xs mb-1">{{ key }}</div>
              <div class="text-gray-800 text-sm break-words">{{ value }}</div>
            </div>
          </div>
        </div>
        <div
          v-else
          class="border rounded p-3 bg-gray-50 flex-1 flex items-center justify-center text-gray-500 text-sm"
        >
          點擊圖譜中的節點查看詳細資訊
        </div>
      </section>
    </main>
    <!-- toasts -->
    <div class="fixed bottom-4 right-4 space-y-2">
      <div
        v-for="t in toasts"
        :key="t.id"
        class="px-3 py-2 bg-red-500 text-white text-sm rounded shadow"
      >
        {{ t.text }}
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 依需求可再補少量樣式 */
</style>
