{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"pinia": "^3.0.3", "vis-network": "^10.0.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "prettier": "3.6.2", "tailwindcss": "^3.4.13", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}