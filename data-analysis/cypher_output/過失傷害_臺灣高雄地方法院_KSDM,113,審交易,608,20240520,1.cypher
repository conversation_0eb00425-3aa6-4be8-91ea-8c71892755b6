MERGE (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'});

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '05', judgment_day: '20', case_type: '審交易', case_number: '608', title: '過失傷害', judgment_type: '', judgment_date: '2024-05-20', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%af%a9%e4%ba%a4%e6%98%93%2c608%2c20240520%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,審交易,608,20240520,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_被告_1'}) SET p += {name: '陳玥彤', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,審交易,608,20240520,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_告訴人_1'}) SET p += {name: '楊幼朱', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,審交易,608,20240520,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_檢察官_1'}) SET p += {name: '鄭舒倪', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,審交易,608,20240520,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_法官_1'}) SET p += {name: '張嘉芳', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,審交易,608,20240520,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_書記官_1'}) SET p += {name: '周耿瑩', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市三民區同盟一路與自由一路交岔路口'});

MATCH (l:Location {name: '高雄市三民區同盟一路與自由一路交岔路口'}) SET l.type = '交岔路口', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (l:Location {name: '高雄市三民區同盟一路與自由一路交岔路口'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '刑法', law0.description = '過失傷害罪'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MERGE (law1:Law {article: '238'})
SET law1.law_name = '刑事訴訟法', law1.description = '告訴乃論之罪，告訴人於第一審辯論終結前，得撤回其告訴'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MERGE (law2:Law {article: '303'})
SET law2.law_name = '刑事訴訟法', law2.description = '告訴經撤回者，法院應諭知不受理之判決'
MERGE (c)-[:CITES_LAW]->(law2);

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MERGE (law3:Law {article: '452'})
SET law3.law_name = '刑事訴訟法', law3.description = '不經言詞辯論，逕為諭知不受理之判決'
MERGE (c)-[:CITES_LAW]->(law3);

MATCH (c:Case {case_id: 'KSDM,113,審交易,608,20240520,1'})
MERGE (e0:Event {event_id: 'KSDM,113,審交易,608,20240520,1'})
SET e0.event_type = '過失傷害', e0.date_time = '2024-05-20 16:10'
SET e0.reason = '過失傷害'
SET e0.method = '騎乘機車'
SET e0.credibility = 'unknown'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,608,20240520,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,608,20240520,1'})
MATCH (l:Location {name: '高雄市三民區同盟一路與自由一路交岔路口'})
MERGE (e)-[:IN_LOCATION]->(l);