MERGE (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'});

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '01', judgment_day: '11', case_type: '審交易', case_number: '41', title: '過失傷害', judgment_type: '不受理', judgment_date: '2024-01-11', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%af%a9%e4%ba%a4%e6%98%93%2c41%2c20240111%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,審交易,41,20240111,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_被告_1'}) SET p += {name: '陳翔浚', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_1'}) SET p += {name: '蕭湘淇', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_2'});

MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_2'}) SET p += {name: '呂亮亘', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,審交易,41,20240111,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_檢察官_1'}) SET p += {name: '臺灣高雄地方檢察署檢察官', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,審交易,41,20240111,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_法官_1'}) SET p += {name: '賴建旭', role: '法官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,審交易,41,20240111,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_書記官_1'}) SET p += {name: '鄒秀珍', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (o0:Organization {name: '臺灣高雄地方檢察署'});

MATCH (o:Organization {name: '臺灣高雄地方檢察署'}) SET o.org_type = '檢察機關';

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (o:Organization {name: '臺灣高雄地方檢察署'})
MERGE (c)-[:INVOLVES_ORGANIZATION]->(o);

MERGE (l0:Location {name: '臺灣高雄地方檢察署'});

MATCH (l:Location {name: '臺灣高雄地方檢察署'}) SET l.type = '公署', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MATCH (l:Location {name: '臺灣高雄地方檢察署'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MERGE (law0:Law {article: '303'})
SET law0.law_name = '刑事訴訟法', law0.description = '告訴或請求乃論之罪，未經告訴、請求或其告訴、請求經撤回或已逾告訴期間者'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,審交易,41,20240111,1'})
MERGE (e0:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
SET e0.event_type = '過失傷害案件', e0.date_time = '2024-01-11'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_2'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,41,20240111,1_告訴人_2'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,41,20240111,1_ev1'})
MATCH (l:Location {name: '臺灣高雄地方檢察署'})
MERGE (e)-[:IN_LOCATION]->(l);