MERGE (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'});

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '03', judgment_day: '08', case_type: '審交易', case_number: '36', title: '過失傷害', judgment_type: '', judgment_date: '2024-03-08', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%af%a9%e4%ba%a4%e6%98%93%2c36%2c20240308%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,審交易,36,20240308,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_被告_1'}) SET p += {name: '許馨瑩', role: '被告', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,審交易,36,20240308,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_告訴人_1'}) SET p += {name: '陳繹劦', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,審交易,36,20240308,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_檢察官_1'}) SET p += {name: '臺灣高雄地方檢察署檢察官', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,審交易,36,20240308,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_法官_1'}) SET p += {name: '丁亦慧', role: '法官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,審交易,36,20240308,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_書記官_1'}) SET p += {name: '盧重逸', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市鼓山區美術東四路291號'});

MATCH (l:Location {name: '高雄市鼓山區美術東四路291號'}) SET l.type = '事故現場', l.address = '高雄市鼓山區美術東四路291號';

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MATCH (l:Location {name: '高雄市鼓山區美術東四路291號'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '刑法', law0.description = '過失傷害罪'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MERGE (law1:Law {article: '238-1'})
SET law1.law_name = '刑事訴訟法', law1.description = '告訴乃論之罪'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MERGE (law2:Law {article: '303-3'})
SET law2.law_name = '刑事訴訟法', law2.description = '不經言詞辯論為之'
MERGE (c)-[:CITES_LAW]->(law2);

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MERGE (law3:Law {article: '307'})
SET law3.law_name = '刑事訴訟法', law3.description = '判決如主文'
MERGE (c)-[:CITES_LAW]->(law3);

MATCH (c:Case {case_id: 'KSDM,113,審交易,36,20240308,1'})
MERGE (e0:Event {event_id: 'KSDM,113,審交易,36,20240308,1_ev1'})
SET e0.event_type = '過失傷害', e0.date_time = '2022-08-14 09:51'
SET e0.method = '駕駛小客車迴轉'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,審交易,36,20240308,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,36,20240308,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,36,20240308,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,36,20240308,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,36,20240308,1_ev1'})
MATCH (l:Location {name: '高雄市鼓山區美術東四路291號'})
MERGE (e)-[:IN_LOCATION]->(l);