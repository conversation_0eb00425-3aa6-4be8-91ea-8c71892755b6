MERGE (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'});

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '04', judgment_day: '30', case_type: '交簡上', case_number: '37', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-04-30', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e4%ba%a4%e7%b0%a1%e4%b8%8a%2c37%2c20240430%2c1.pdf', verdict_items: ['緩刑: 楊天宇緩刑貳年']};

MERGE (p0:Person {uid: 'KSDM,113,交簡上,37,20240430,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_被告_1'}) SET p += {name: '楊天宇', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,交簡上,37,20240430,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_告訴人_1'}) SET p += {name: '周郁芩', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,交簡上,37,20240430,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_檢察官_1'}) SET p += {name: '鄭舒倪', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,交簡上,37,20240430,1_檢察官_2'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_檢察官_2'}) SET p += {name: '郭麗娟', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_檢察官_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_1'}) SET p += {name: '李貞瑩', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_2'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_2'}) SET p += {name: '粟威穆', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p6:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_3'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_3'}) SET p += {name: '莊維澤', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_法官_3'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p7:Person {uid: 'KSDM,113,交簡上,37,20240430,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_書記官_1'}) SET p += {name: '張宸維', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄地方法院'});

MATCH (l:Location {name: '高雄地方法院'}) SET l.type = '法院', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MATCH (l:Location {name: '高雄地方法院'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '刑法', law0.description = '過失傷害罪'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,37,20240430,1'})
MERGE (e0:Event {event_id: 'KSDM,113,交簡上,37,20240430,1_ev1'})
SET e0.event_type = '過失傷害案件', e0.date_time = '2024-04-30'
SET e0.amount = 980000
SET e0.reason = '和解賠償'
SET e0.method = '賠償'
SET e0.confession = '已與告訴人達成和解'
SET e0.credibility = 'high'
SET e0.evidence_metrics = '和解書、匯款明細'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,37,20240430,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,37,20240430,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,37,20240430,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,37,20240430,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,37,20240430,1_ev1'})
MATCH (l:Location {name: '高雄地方法院'})
MERGE (e)-[:IN_LOCATION]->(l);