MERGE (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'});

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '05', judgment_day: '20', case_type: '審交易', case_number: '442', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-05-20', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%af%a9%e4%ba%a4%e6%98%93%2c442%2c20240520%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,審交易,442,20240520,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_被告_1'}) SET p += {name: '葛伯然', role: '被告', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,審交易,442,20240520,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_告訴人_1'}) SET p += {name: '甲○○', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: True};

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,審交易,442,20240520,1_被害人_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_被害人_1'}) SET p += {name: '蘇○○', role: '被害人', gender: '女', relationship_to_others: '告訴人之子女', is_anonymous: True};

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_被害人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,審交易,442,20240520,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_檢察官_1'}) SET p += {name: '臺灣高雄地方檢察署檢察官', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,審交易,442,20240520,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_法官_1'}) SET p += {name: '李宜穎', role: '法官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,審交易,442,20240520,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_書記官_1'}) SET p += {name: '林雅婷', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市鼓山區九如四路1602號'});

MATCH (l:Location {name: '高雄市鼓山區九如四路1602號'}) SET l.type = '地址', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MATCH (l:Location {name: '高雄市鼓山區九如四路1602號'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '刑法', law0.description = '過失傷害罪'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,審交易,442,20240520,1'})
MERGE (e0:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
SET e0.event_type = '交通事故', e0.date_time = '2024-06-08 17:24'
SET e0.method = '超車'
SET e0.confession = '被告於車禍發生後，犯罪未被發覺前，在現場等候，並於警方到場時，自首而受裁判。'
SET e0.credibility = 'unknown'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,442,20240520,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
MATCH (p:Person {uid: '甲○○'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
MATCH (p:Person {uid: '蘇○○'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
MATCH (p:Person {uid: '甲○○'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
MATCH (p:Person {uid: '蘇○○'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,442,20240520,1_ev1'})
MATCH (l:Location {name: '高雄市鼓山區九如四路1602號'})
MERGE (e)-[:IN_LOCATION]->(l);