MERGE (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'});

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '03', judgment_day: '27', case_type: '審交易', case_number: '394', title: '過失傷害', judgment_type: '', judgment_date: '2024-03-27', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%af%a9%e4%ba%a4%e6%98%93%2c394%2c20240327%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,審交易,394,20240327,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_被告_1'}) SET p += {name: '羅騰峰', role: '被告', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,審交易,394,20240327,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_告訴人_1'}) SET p += {name: '熊Ｏ泰', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,審交易,394,20240327,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_檢察官_1'}) SET p += {name: '臺灣高雄地方檢察署檢察官', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,審交易,394,20240327,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_法官_1'}) SET p += {name: '莊珮君', role: '法官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,審交易,394,20240327,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_書記官_1'}) SET p += {name: '林玉珊', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市三民區覺民路288巷'});

MATCH (l:Location {name: '高雄市三民區覺民路288巷'}) SET l.type = '地點', l.address = '高雄市三民區覺民路288巷';

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MATCH (l:Location {name: '高雄市三民區覺民路288巷'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MERGE (law0:Law {article: '第86條第1項第2款'})
SET law0.law_name = '修正後道路交通管理處罰條例', law0.description = '無駕駛執照駕車因而過失致人受傷罪嫌'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MERGE (law1:Law {article: '第284條前段'})
SET law1.law_name = '刑法', law1.description = '無駕駛執照駕車因而過失致人受傷罪嫌'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'KSDM,113,審交易,394,20240327,1'})
MERGE (e0:Event {event_id: 'KSDM,113,審交易,394,20240327,1_ev1'})
SET e0.event_type = '過失傷害', e0.date_time = '2024-03-27 08:18'
SET e0.method = '騎乘普通重型機車'
SET e0.confession = '被告於車禍發生後，自首而受裁判。'
SET e0.credibility = 'unknown'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,審交易,394,20240327,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,394,20240327,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,394,20240327,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,394,20240327,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,394,20240327,1_ev1'})
MATCH (l:Location {name: '高雄市三民區覺民路288巷'})
MERGE (e)-[:IN_LOCATION]->(l);