MERGE (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'});

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '06', judgment_day: '18', case_type: '交簡上', case_number: '8', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-06-18', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e4%ba%a4%e7%b0%a1%e4%b8%8a%2c8%2c20240618%2c1.pdf', verdict_items: ['緩刑: 黃詒慧緩刑貳年']};

MERGE (p0:Person {uid: 'KSDM,113,交簡上,8,20240618,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_被告_1'}) SET p += {name: '黃詒慧', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,交簡上,8,20240618,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_告訴人_1'}) SET p += {name: '丁阿香', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,交簡上,8,20240618,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_檢察官_1'}) SET p += {name: '吳政洋', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,交簡上,8,20240618,1_檢察官_2'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_檢察官_2'}) SET p += {name: '李佳韻', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_檢察官_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_1'}) SET p += {name: '蔡書瑜', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_2'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_2'}) SET p += {name: '劉珊秀', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p6:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_3'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_3'}) SET p += {name: '黃偉竣', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_法官_3'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p7:Person {uid: 'KSDM,113,交簡上,8,20240618,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_書記官_1'}) SET p += {name: '吳和卿', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (o0:Organization {name: '臺灣高雄地方檢察署'});

MATCH (o:Organization {name: '臺灣高雄地方檢察署'}) SET o.org_type = '檢察機關';

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (o:Organization {name: '臺灣高雄地方檢察署'})
MERGE (c)-[:INVOLVES_ORGANIZATION]->(o);

MERGE (o1:Organization {name: '國軍高雄總醫院'});

MATCH (o:Organization {name: '國軍高雄總醫院'}) SET o.org_type = '醫院';

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (o:Organization {name: '國軍高雄總醫院'})
MERGE (c)-[:INVOLVES_ORGANIZATION]->(o);

MERGE (l0:Location {name: '高雄市苓雅區正義路與文昌路之交岔路口'});

MATCH (l:Location {name: '高雄市苓雅區正義路與文昌路之交岔路口'}) SET l.type = '交岔路口', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MATCH (l:Location {name: '高雄市苓雅區正義路與文昌路之交岔路口'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '刑法第284條', law0.description = '因過失傷害人者，處1年以下有期徒刑、拘役或10萬元以下罰金；致重傷者，處3年以下有期徒刑、拘役或30萬元以下罰金。'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,8,20240618,1'})
MERGE (e0:Event {event_id: 'KSDM,113,交簡上,8,20240618,1_ev1'})
SET e0.event_type = '過失傷害', e0.date_time = '2023-12-22 07:50'
SET e0.confession = '被告黃詒慧於本院第二審審判程序中之自白'
SET e0.credibility = 'unknown'
SET e0.evidence_metrics = '道路交通事故調查報告表'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,8,20240618,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,8,20240618,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,8,20240618,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,8,20240618,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,8,20240618,1_ev1'})
MATCH (l:Location {name: '高雄市苓雅區正義路與文昌路之交岔路口'})
MERGE (e)-[:IN_LOCATION]->(l);