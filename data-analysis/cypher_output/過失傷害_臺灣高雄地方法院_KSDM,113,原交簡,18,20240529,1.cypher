MERGE (c:Case {case_id: '<PERSON><PERSON><PERSON>,113,原交簡,18,20240529,1'});

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '05', judgment_day: '29', case_type: '原交簡', case_number: '18', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-05-29', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%8e%9f%e4%ba%a4%e7%b0%a1%2c18%2c20240529%2c1.pdf', verdict_items: ['有期徒刑: 處有期徒刑參月，如易科罰金，以新臺幣壹仟元折算壹日。']};

MERGE (p0:Person {uid: '<PERSON><PERSON><PERSON>,113,原交簡,18,20240529,1_被告_1'});

MATCH (p:Person {uid: '<PERSON><PERSON><PERSON>,113,原交簡,18,20240529,1_被告_1'}) SET p += {name: '吳致誠', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,原交簡,18,20240529,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_告訴人_1'}) SET p += {name: '陶秉業', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,原交簡,18,20240529,1_辯護人_1'});

MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_辯護人_1'}) SET p += {name: '陳佳煒', role: '辯護人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_辯護人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,原交簡,18,20240529,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_檢察官_1'}) SET p += {name: '鄭舒倪', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,原交簡,18,20240529,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_法官_1'}) SET p += {name: '姚億燦', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,原交簡,18,20240529,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_書記官_1'}) SET p += {name: '李欣妍', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市鳳山區中山東路380巷與環河街口'});

MATCH (l:Location {name: '高雄市鳳山區中山東路380巷與環河街口'}) SET l.type = 'intersection', l.address = '高雄市鳳山區中山東路380巷與環河街口';

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MATCH (l:Location {name: '高雄市鳳山區中山東路380巷與環河街口'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '中華民國刑法第284條', law0.description = '因過失傷害人者，處1年以下有期徒刑、拘役或10萬元以下罰金；致重傷者，處3年以下有期徒刑、拘役或30萬元以下罰金。'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MERGE (law1:Law {article: '93-1-2'})
SET law1.law_name = '道路交通安全規則第93條第1項第2款', law1.description = '汽車（包括機車）行至無號誌之交岔路口，應減速慢行，作隨時停車之準備。'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MERGE (law2:Law {article: '102-1-11'})
SET law2.law_name = '道路交通安全規則第102條第1項第11款', law2.description = '交岔路口因特殊需要另設有標誌、標線者，並應依其指示行車；「停」標字，用以指示車輛至此必須停車再開。'
MERGE (c)-[:CITES_LAW]->(law2);

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MERGE (law3:Law {article: '177-1'})
SET law3.law_name = '道路交通標誌標線號誌設置規則第177條第1項', law3.description = '設置交通標誌、標線應依據相關規定進行。'
MERGE (c)-[:CITES_LAW]->(law3);

MATCH (c:Case {case_id: 'KSDM,113,原交簡,18,20240529,1'})
MERGE (e0:Event {event_id: '1'})
SET e0.event_type = '交通事故', e0.date_time = '2024-05-29 23:50'
SET e0.reason = '過失傷害'
SET e0.method = '交通事故'
SET e0.confession = '吳致誠於車禍發生後，犯罪未被發覺前，在現場等候，並於警方到場時，自首而受裁判。'
SET e0.credibility = 'high'
SET e0.evidence_metrics = '監視器錄影檔案及翻拍照片'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: '1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: '1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: '1'})
MATCH (p:Person {uid: 'KSDM,113,原交簡,18,20240529,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: '1'})
MATCH (l:Location {name: '高雄市鳳山區中山東路380巷與環河街口'})
MERGE (e)-[:IN_LOCATION]->(l);