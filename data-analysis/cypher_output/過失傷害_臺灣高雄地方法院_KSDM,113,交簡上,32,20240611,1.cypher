MERGE (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'});

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '06', judgment_day: '11', case_type: '交簡上', case_number: '32', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-06-11', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e4%ba%a4%e7%b0%a1%e4%b8%8a%2c32%2c20240611%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,交簡上,32,20240611,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_被告_1'}) SET p += {name: '李駿暉', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,交簡上,32,20240611,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_告訴人_1'}) SET p += {name: '黃啓雄', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,交簡上,32,20240611,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_檢察官_1'}) SET p += {name: '張靜怡', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,交簡上,32,20240611,1_檢察官_2'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_檢察官_2'}) SET p += {name: '伍振文', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_檢察官_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_1'}) SET p += {name: '吳書嫺', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_2'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_2'}) SET p += {name: '陳一誠', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p6:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_3'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_3'}) SET p += {name: '陳永盛', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_法官_3'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p7:Person {uid: 'KSDM,113,交簡上,32,20240611,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_書記官_1'}) SET p += {name: '陳予盼', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市新興區開封路與八德一路口'});

MATCH (l:Location {name: '高雄市新興區開封路與八德一路口'}) SET l.type = 'intersection', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MATCH (l:Location {name: '高雄市新興區開封路與八德一路口'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MERGE (law0:Law {article: '86-1-5'})
SET law0.law_name = '修正後道路交通管理處罰條例', law0.description = '汽車駕駛人行近行人穿越道不依規定讓行人優先通行而過失致人傷害罪'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MERGE (law1:Law {article: '284'})
SET law1.law_name = '刑法', law1.description = '因過失傷害人者，處1年以下有期徒刑、拘役或10萬元以下罰金；致重傷者，處3年以下有期徒刑、拘役或30萬元以下罰金。'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'KSDM,113,交簡上,32,20240611,1'})
MERGE (e0:Event {event_id: 'KSDM,113,交簡上,32,20240611,1_ev1'})
SET e0.event_type = '過失傷害', e0.date_time = '2022-12-18 13:07'
SET e0.method = '駕駛車輛未依規定讓行人優先通行'
SET e0.confession = '李駿暉承認駕車有過失'
SET e0.credibility = 'high'
SET e0.evidence_metrics = '現場照片、診斷證明書等'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,32,20240611,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,32,20240611,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,32,20240611,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,交簡上,32,20240611,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,交簡上,32,20240611,1_ev1'})
MATCH (l:Location {name: '高雄市新興區開封路與八德一路口'})
MERGE (e)-[:IN_LOCATION]->(l);