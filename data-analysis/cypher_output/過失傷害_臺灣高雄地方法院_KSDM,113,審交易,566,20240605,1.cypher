MERGE (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'});

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '06', judgment_day: '05', case_type: '審交易', case_number: '566', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-06-05', court: '臺灣高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e5%af%a9%e4%ba%a4%e6%98%93%2c566%2c20240605%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_1'}) SET p += {name: '陳權成', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_2'});

MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_2'}) SET p += {name: '楊敏暄', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_2'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,審交易,566,20240605,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_檢察官_1'}) SET p += {name: '臺灣高雄地方檢察署檢察官', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,審交易,566,20240605,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_法官_1'}) SET p += {name: '翁碧玲', role: '法官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,審交易,566,20240605,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_書記官_1'}) SET p += {name: '陳郁惠', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: None};

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市大寮區仁愛路279巷'});

MATCH (l:Location {name: '高雄市大寮區仁愛路279巷'}) SET l.type = '交岔路口', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MATCH (l:Location {name: '高雄市大寮區仁愛路279巷'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MERGE (law0:Law {article: '86-1'})
SET law0.law_name = '道路交通管理處理罰例', law0.description = '無駕駛執照駕車'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,審交易,566,20240605,1'})
MERGE (e0:Event {event_id: 'KSDM,113,審交易,566,20240605,1_ev1'})
SET e0.event_type = '過失傷害', e0.date_time = '2023-10-24 10:59'
SET e0.method = '騎乘普通重型機車'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,審交易,566,20240605,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,566,20240605,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_2'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,566,20240605,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,566,20240605,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,審交易,566,20240605,1_被告_2'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,審交易,566,20240605,1_ev1'})
MATCH (l:Location {name: '高雄市大寮區仁愛路279巷'})
MERGE (e)-[:IN_LOCATION]->(l);