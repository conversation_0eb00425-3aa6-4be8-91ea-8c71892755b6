MERGE (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'});

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '04', judgment_day: '17', case_type: '簡', case_number: '597', title: '過失傷害', judgment_type: 'substantive', judgment_date: '2024-04-17', court: '高雄地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/KSDM/113%2c%e7%b0%a1%2c597%2c20240417%2c1.pdf', verdict_items: ['拘役: 處拘役貳拾日，如易科罰金，以新臺幣壹仟元折算壹日']};

MERGE (p0:Person {uid: 'KSDM,113,簡,597,20240417,1_被告_1'});

MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_被告_1'}) SET p += {name: '張皓瑋', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_被告_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p1:Person {uid: 'KSDM,113,簡,597,20240417,1_告訴人_1'});

MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_告訴人_1'}) SET p += {name: '黃于軒', role: '告訴人', gender: '', relationship_to_others: '原為男女朋友', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_告訴人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p2:Person {uid: 'KSDM,113,簡,597,20240417,1_證人_1'});

MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_證人_1'}) SET p += {name: '周芫陞', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_證人_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p3:Person {uid: 'KSDM,113,簡,597,20240417,1_檢察官_1'});

MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_檢察官_1'}) SET p += {name: '羅水郎', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_檢察官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p4:Person {uid: 'KSDM,113,簡,597,20240417,1_法官_1'});

MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_法官_1'}) SET p += {name: '姚億燦', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_法官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (p5:Person {uid: 'KSDM,113,簡,597,20240417,1_書記官_1'});

MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_書記官_1'}) SET p += {name: '李欣妍', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_書記官_1'})
MERGE (c)-[:HAS_PERSON]->(p);

MERGE (l0:Location {name: '高雄市○○區○○路000巷0號5樓'});

MATCH (l:Location {name: '高雄市○○區○○路000巷0號5樓'}) SET l.type = '租屋處', l.address = '';

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MATCH (l:Location {name: '高雄市○○區○○路000巷0號5樓'})
MERGE (c)-[:INVOLVES_LOCATION]->(l);

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MERGE (law0:Law {article: '284'})
SET law0.law_name = '刑法', law0.description = '因過失傷害人者，處一年以下有期徒刑、拘役或十萬元以下罰金；致重傷者，處三年以下有期徒刑、拘役或三十萬元以下罰金。'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'KSDM,113,簡,597,20240417,1'})
MERGE (e0:Event {event_id: 'KSDM,113,簡,597,20240417,1_ev1'})
SET e0.event_type = '過失傷害', e0.date_time = '112-07-30 23:30'
SET e0.confession = '張皓瑋坦承犯行'
SET e0.credibility = 'high'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'KSDM,113,簡,597,20240417,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,簡,597,20240417,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_告訴人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,簡,597,20240417,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_證人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'KSDM,113,簡,597,20240417,1_ev1'})
MATCH (p:Person {uid: 'KSDM,113,簡,597,20240417,1_告訴人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'KSDM,113,簡,597,20240417,1_ev1'})
MATCH (l:Location {name: '高雄市○○區○○路000巷0號5樓'})
MERGE (e)-[:IN_LOCATION]->(l);