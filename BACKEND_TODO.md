# 後端 TODO（Python / FastAPI / Neo4j / OpenAI）

依據 SPEC.md，並基於以下已確認條件：
- LLM 模型：OpenAI GPT-4o-mini
- 允許後端啟動時進行 Neo4j schema 只讀擷取（可啟用/關閉）
- 前端圖譜：vis-network

---

## 0) 專案基礎
- [ ] 建立基本結構：app/main.py, app/api/, app/services/, app/models/, app/utils/, app/config/
- [ ] 建立 README（後端）與執行指南（含 .env 範例）
- [ ] 定義 pyproject.toml 或 requirements.txt（僅列依賴，不安裝）

## 1) 環境設定
- [ ] .env 與設定讀取（pydantic BaseSettings 或 dotenv）
- [ ] 參數：NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
- [ ] 參數：OPENAI_API_KEY, MODEL_NAME=gpt-4o-mini
- [ ] 參數：QUERY_LIMIT(預設100), QUERY_TIMEOUT_MS(預設8000)
- [ ] 參數：SCHEMA_INTROSPECT(true/false), SCHEMA_CACHE_PATH=schema/schema.json
- [ ] 參數：ALLOWED_ORIGINS（前端來源）

## 2) Neo4j 連線（只讀）
- [ ] 建立 neo4j-driver 連線工廠與只讀 session helper（逾時設定）
- [ ] 寫最小 health check（連線可測）

## 3) Schema 解析與快取
- [ ] Parser：讀取 data-analysis/cypher_output/*，抽取 labels/relationships/properties
- [ ] Parser：解析 data-analysis/verdict_graph.py（若內含明確定義則納入）
- [ ] 可選 Introspect：CALL db.schema.visualization() / SHOW *（需權限，受 SCHEMA_INTROSPECT 控制）
- [ ] 規則：合併、去重、精簡（僅必要屬性）
- [ ] 產出精簡描述並寫入 SCHEMA_CACHE_PATH
- [ ] 提供 get_schema()，不存在時觸發生成

## 4) LLM 服務（兩步）
- [ ] 步驟一：產生 Cypher 的 Prompt 與請求封裝（溫度 0–0.2）
- [ ] 安全：要求輸出 JSON { cypher, params?, intent? }
- [ ] 後端驗證：黑名單（CREATE/MERGE/DELETE/SET/DROP 等）、只讀白名單、強制/追加 LIMIT
- [ ] 步驟二：產生回答的 Prompt 與請求封裝（溫度 0.3–0.5）
- [ ] 逾時與錯誤處理（簡短錯誤訊息、一次重試可選）

## 5) 查詢執行與圖資料映射
- [ ] 執行安全的 Cypher（只讀 transaction）
- [ ] 將結果映射為 graph：
      nodes: [{ id, label, type }], edges: [{ id, source, target, label }]
- [ ] 將 resultCount、durationMs 計算並回傳

## 6) API 實作（FastAPI）
- [ ] POST /api/chat：整合流程（schema→LLM1→驗證→Neo4j→LLM2→組裝回應）
- [ ] GET /api/examples：回傳 3–5 個預設範例
- [ ] GET /api/schema：回傳 SCHEMA_CACHE_PATH 內容
- [ ] POST /api/schema/refresh（可選）：重新解析/擷取後覆蓋快取
- [ ] 啟用 CORS（限定前端來源）

## 7) 日誌與可觀測性（簡易）
- [ ] 統一日誌格式：reqId, path, durationMs, model, resultCount（避免個資）
- [ ] 錯誤日誌：LLM/Cypher 驗證/Neo4j 逾時

## 8) 測試（pytest）
- [ ] 單元：Cypher 安全驗證（黑名單、LIMIT 附加、白名單）
- [ ] 單元：Schema 解析（對 cypher_output 與 verdict_graph.py 的 parser）
- [ ] 單元：Prompt 封裝（輸入/輸出格式）
- [ ] 整合：/api/chat 正常、查無資料、不合法 Cypher（mock LLM/Neo4j）

## 9) 文件與運行
- [ ] README：如何設定 .env、啟動 Uvicorn、開啟互動 API Docs (/docs)
- [ ] 開發 run 指南：前端/後端同機啟動方式
- [ ] 範例請求/回應樣本（便於論文附錄）

## 10)（可選）容器化與部署
- [ ] Dockerfile（僅後端）
- [ ] 簡單 Compose（若需）

---

## 驗收標準（Minimum Acceptance Criteria）
- [ ] 啟動後 /docs 可存取，/api/examples 有資料
- [ ] /api/schema 能回傳精簡 schema（由 data-analysis/* 解析或 introspect）
- [ ] /api/chat 能根據提問產生只讀 Cypher、查 Neo4j、回傳答案＋圖資料
- [ ] 對於無結果與不合法查詢有清楚提示
- [ ] 預設 LIMIT 生效、逾時設定生效、關鍵字黑名單生效

