## 法律知識圖譜問答系統（MVP）規格書 — Python 後端版

本文件描述一個最小可行（學生版、從簡）的系統規格：使用者在前端輸入法律問題，後端利用 LLM 產生 Cypher 查詢 Neo4j，將查詢結果轉成簡潔文字答案，並在前端顯示對應的節點關係圖。

---

### 1. 目標與非目標
- 目標：
  - 極簡聊天介面（Vue 3 + Tailwind，JavaScript），可輸入問題、顯示回答與圖。
  - 後端改為 Python（便於與現有資料分析整合），只做必要 API。
  - LLM 根據 Neo4j schema 產生正確且「只讀」的 Cypher；查詢結果回傳簡短中文回答與可視化資料。
- 非目標：
  - 不做帳號系統、權限管理、花俏動畫或商業化設計。
  - 不做大型部署與監控；僅提供最小的本機/單機部署方式。

---

### 2. 技術棧與依賴
- 前端：
  - Vue 3（Vite）、TailwindCSS、JavaScript（不用 TypeScript）。
  - 圖譜套件：Cytoscape.js 或 vis-network（二擇一，取其一並保持簡單）。
- 後端（Python）：
  - 框架：FastAPI（理由：結構簡潔、型別提示友善、內建互動 API Docs）。
  - 服務啟動：Uvicorn。
  - Neo4j 連線：neo4j-driver（官方 Python 驅動）。
  - LLM：OpenAI 相容 API（openai Python 套件或相容 SDK）。
  - 其他：pydantic（請求/回應模型）、tenacity（可選，用於簡易重試）。
- 資料庫：Neo4j（只讀）。

---

### 3. 系統架構與資料流程
- 架構：
  - 前端 SPA（聊天區＋範例區＋圖譜區）。
  - 後端 API（FastAPI）：
    1) 依據 schema 與使用者問題呼叫 LLM 產生 Cypher（步驟一），
    2) 執行 Cypher 查 Neo4j，
    3) 將查詢結果與原問題給 LLM 產生簡短答案（步驟二），
    4) 回傳文字答案、圖資料（nodes/edges）、實際執行的 Cypher、簡短元資料。
- 單次對話資料流：
  1) 前端送出問題
  2) 後端載入/產生 schema 描述 → LLM 生成 Cypher（溫度低）
  3) 後端驗證 Cypher 安全性 → 執行 Neo4j 查詢（加 LIMIT、逾時）
  4) 後端將結果摘要化（可再喚起 LLM）
  5) 回傳前端：answerText + graph + query + meta

---

### 4. Neo4j Schema 來源與管理
- 來源檔案（既有）：
  - data-analysis/cypher_output/ 目錄下的檔案（包含先前產生的 Cypher/結構資訊）。
  - data-analysis/verdict_graph.py（內含萃取的結構與產生 Cypher 的程式碼，可參考其中的節點/關係定義）。
- 生成策略（後端啟動流程）：
  1) 優先讀取本地快取 schema（例如 schema/schema.json），若存在直接使用。
  2) 若無快取：
     - 嘗試解析 data-analysis/cypher_output 下的檔案，抽取節點標籤、關係類型、常見屬性，組成精簡 schema 描述。
     - 補充參考 data-analysis/verdict_graph.py 內部的結構定義（若有明確的 label/type/property 列表，納入描述）。
  3) 若仍不足，以 Neo4j 只讀指令（例如 CALL db.schema.visualization() 或 SHOW LABELS/RELATIONSHIPS/INDEXES）做最後補足（可選，需權限）。
  4) 產生精簡 schema 描述並快取為 schema/schema.json，後續 LLM Prompt 使用該描述。
- 精簡 schema 描述內容：
  - 節點標籤（Label）及常見屬性（屬性名稱與基本型別）。
  - 關係型別（Type），端點標籤（起點→終點），常見屬性。
- 刷新機制：
  - 提供 /api/schema/refresh（可選）重新解析檔案/資料庫並覆蓋快取。

---

### 5. LLM Prompt 設計（極簡、兩步）
- 步驟一：產生 Cypher
  - 輸入：
    - 使用者問題（中文），
    - 精簡 schema 描述（節點/關係/屬性），
    - 約束規則：只讀、必須使用既有標籤/關係/屬性、必須包含 LIMIT（預設 100、可設），禁止 CREATE/MERGE/DELETE/SET/DROP 等寫入語句。
  - 輸出（JSON 風格）：
    - { cypher: string, params?: object, intent?: string }
  - 後端安全檢查：
    - 關鍵字黑名單、只讀白名單、無 LIMIT 則自動附加，最大 LIMIT 受環境變數約束。
- 步驟二：產生回答
  - 輸入：
    - 使用者問題、查詢結果（結構化 JSON）、實際執行的 cypher（做為背景，不要求模型重印）
  - 輸出：
    - 簡短中文答案（2–6 句）；無結果時給 1–2 條改寫建議。
- 參數建議：
  - 產生 Cypher 溫度低（0–0.2），產生回答溫度中低（0.3–0.5）。

---

### 6. API 介面（FastAPI）
- POST /api/chat
  - Request: { message: string, history?: [{role:'user'|'assistant', content:string}] }
  - Response: {
      answerText: string,
      graph: { nodes: [{id, label, type}], edges: [{id, source, target, label}] },
      query: { cypher: string, params?: object },
      meta: { durationMs, model, resultCount }
    }
- GET /api/examples
  - Response: [{ id, title, message }]
- GET /api/schema
  - Response: { labels: [...], relationships: [...], properties?: {...} }
- POST /api/schema/refresh（可選）
  - Response: { ok: boolean }

---

### 7. 前端介面規格（極簡）
- 結構：
  - Header：標題「法律知識圖譜問答（MVP）」＋「清空對話」。
  - 範例列：3–5 個按鈕，點擊自動填入並送出。
  - 聊天區：訊息泡泡；Loading 狀態（生成查詢中/查詢資料中/產生回答中）。
  - 圖譜區：Cytoscape.js（或 vis-network）；工具列（Fit/Reset）；簡單圖例（節點顏色依 type）。
- 視覺：
  - Tailwind 基本樣式，素雅；不做花俏動畫。
- 圖資料映射：
  - nodes: { id, label(顯示名), type(節點標籤) }
  - edges: { id, source, target, label(關係型別) }
  - 上限：節點≤100、邊≤200；避免卡頓。

---

### 8. 錯誤處理與安全
- 錯誤處理：
  - LLM 產生的 Cypher 不合法 → 回傳簡訊息並可自動重試一次（最多一次）。
  - Neo4j 無資料 → 明確顯示「查無資料」，附 1–2 條改寫建議。
  - 逾時/例外 → 顯示簡潔錯誤訊息，建議稍後重試。
- 安全策略：
  - 強制只讀：拒絕 CREATE/MERGE/DELETE/SET/DROP 等；僅允許 MATCH/RETURN/WITH/OPTIONAL MATCH 等。
  - 自動添加/限制 LIMIT；查詢逾時（如 5–10 秒）。
  - CORS 僅允許本前端來源（開發期可為 localhost）。
  - API Key 僅存於後端環境變數，不在前端暴露。

---

### 9. 測試計畫（簡易）
- 後端單元測試（pytest）：
  - Cypher 安全驗證（黑名單、LIMIT 附加）。
  - Schema 解析（從 data-analysis/cypher_output 與 verdict_graph.py 產生描述）。
- 後端整合測試：
  - /api/chat 正常路徑、查無資料、LLM 輸出不合法。
- 前端簡易測試：
  - UI 渲染、送出動作、Loading 顯示、錯誤提示。
- 手動驗收：
  - 預設 3–5 個範例問題可得到「合理」答案與圖譜顯示。

---

### 10. 部署與環境變數
- 開發：
  - 前端：Vite 開發伺服器。
  - 後端：Uvicorn 啟動 FastAPI。
- 環境變數（.env）：
  - NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
  - OPENAI_API_KEY（或相容提供者）
  - QUERY_LIMIT（預設 100）、QUERY_TIMEOUT_MS（預設 8000）
  - MODEL_NAME（如 gpt-4o-mini / gpt-4.1-mini 或相容）
- 部署（單機）：
  - 後端以 Uvicorn/pm2 啟動；前端打包後由任意靜態伺服器或 Nginx 提供。

---

### 11. 查詢範例（前端內建）
- 「公司甲與乙之間是否存在契約或交易關係？」
- 「人名A涉及哪些案件？與哪些公司/人有關聯？」
- 「某法條（例如民法第X條）涉及的案件或判決有哪些？」
- 「此公司近年與哪些人或案件有互動關係？」
- 「案件B涉及的主要當事人與關係類型？」
（依實際 schema 調整措辭，避免模型亂猜）

---

### 12. 後續可擴充（非必要）
- 點擊節點展開鄰接節點。
- 回答中提供「使用的 Cypher」收合區塊（透明化、便於論文附錄）。
- 簡單對話記憶（僅 sessionStorage），不做後端儲存。
- 多輪澄清問答（模型提問補參數）。

---

### 13. 需要你確認/提供
- LLM 供應商與具體模型（成本 vs. 準確度）。
  A: OpenAI GPT-4o-mini
- 可否允許後端在啟動時存取 Neo4j 做 schema 補足（若不能，則僅以 data-analysis/ 內容生成描述）。
  A: 可以
- 前端圖譜套件偏好（Cytoscape.js 或 vis-network）。
  A: vis-network

---

### 14. 簡短結語
- 保持「學生、從簡」：問 → 產生 Cypher → 查 Neo4j → 答案 + 圖。
- 架構清晰、實作可控、可在口試中清楚說明。

