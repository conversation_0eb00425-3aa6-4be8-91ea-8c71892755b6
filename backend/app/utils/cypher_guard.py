import re
from app.config.settings import get_settings

# 非嚴格版，只做 MVP 基本保護
FORBIDDEN = re.compile(r"\b(CREATE|MERGE|DELETE|SET|DROP)\b", re.IGNORECASE)


def ensure_readonly_cypher(cypher: str) -> str:
    if FORBIDDEN.search(cypher or ""):
        raise ValueError("查詢包含不允許的寫入語句")

    # 確保 LIMIT 存在，沒有就加
    if re.search(r"\bLIMIT\s+\d+\b", cypher, re.IGNORECASE) is None:
        limit = get_settings().query_limit
        # 盡量在結尾加 LIMIT
        return cypher.rstrip().rstrip(";") + f" LIMIT {limit}"
    return cypher

