import re
from pathlib import Path
from typing import Any, Dict, List


def parse_schema_from_sources(cypher_dir: Path, verdict_graph_path: Path) -> Dict[str, Any]:
    """
    從 cypher_output 與 verdict_graph.py 中萃取精簡 schema 描述。
    只做 MVP 級別的統計與推斷：
      - labels：從 MERGE/MATCH (...) 抽出節點標籤
      - relationships：從 -[:TYPE]-> 抽出關係型別
      - properties：嘗試為每個 Label 統計常見屬性
    """
    labels: set[str] = set()
    relationships: set[str] = set()
    properties: Dict[str, set[str]] = {}

    def add_prop(label: str, prop: str):
        if not label or not prop:
            return
        properties.setdefault(label, set()).add(prop)

    # 掃描 cypher 檔案，逐行建立變數到 Label 的簡易映射
    if cypher_dir.exists():
        for p in cypher_dir.glob("*.cypher"):
            var2label: Dict[str, str] = {}
            for line in p.read_text(encoding="utf-8", errors="ignore").splitlines():
                # 1) 擷取 (var:Label { ... }) 與 (var:Label)
                for m in re.finditer(r"\(([a-zA-Z]\w*):([A-Z][A-Za-z0-9_]*)\s*(\{[^}]*\})?\)", line):
                    var, label, obj = m.group(1), m.group(2), m.group(3)
                    var2label[var] = label
                    labels.add(label)
                    if obj:
                        # 抽取 map keys
                        for k in re.findall(r"([A-Za-z_][A-Za-z0-9_]*)\s*:\s*", obj):
                            add_prop(label, k)
                # 2) 擷取 -[:TYPE]->
                for m in re.finditer(r":\s*([A-Z][A-Za-z0-9_]*?)\s*\]", line):
                    relationships.add(m.group(1))
                # 3) 擷取 SET var.prop = ...
                for m in re.finditer(r"SET\s+([a-zA-Z]\w*)\.([A-Za-z_][A-Za-z0-9_]*)\s*=", line):
                    var, prop = m.group(1), m.group(2)
                    label = var2label.get(var)
                    if label:
                        add_prop(label, prop)
                # 4) 擷取 SET var += { ... }
                m = re.search(r"SET\s+([a-zA-Z]\w*)\s*\+=\s*\{([^}]*)\}", line)
                if m:
                    var = m.group(1)
                    obj = m.group(2)
                    label = var2label.get(var)
                    if label:
                        for k in re.findall(r"([A-Za-z_][A-Za-z0-9_]*)\s*:\s*", obj):
                            add_prop(label, k)

    # 從 verdict_graph.py 嘗試補充常見類型名與屬性名（若在程式中以字串出現）
    if verdict_graph_path.exists():
        py_text = verdict_graph_path.read_text(encoding="utf-8", errors="ignore")
        for kw in ["Case", "Person", "Organization", "Location", "Law", "Event"]:
            if kw in py_text:
                labels.add(kw)
        # 根據實際資料結構補充常見欄位
        for lbl, props in {
            "Case": ["case_id", "case_year", "judgment_year", "judgment_month", "judgment_day", "case_type", "case_number", "title", "judgment_type", "judgment_date", "court", "pdf_url", "verdict_items"],
            "Person": ["name", "role", "gender", "relationship_to_others", "is_anonymous", "uid"],
            "Location": ["name", "type", "address"],
            "Law": ["law_name", "article", "description"],
        }.items():
            for k in props:
                add_prop(lbl, k)

    return {
        "labels": sorted(labels),
        "relationships": sorted(relationships),
        "properties": {k: sorted(list(v)) for k, v in properties.items()},
    }


def introspect_schema() -> Dict[str, Any]:
    """
    可選：從 Neo4j 取得 schema（需要權限），此處返回簡化資訊。
    實作層面可改為 MATCH (n) RETURN labels(n) limit 1 等 fallback。
    為了不引入連線依賴，MVP 先回傳空結構，讓流程不中斷。
    """
    return {"labels": [], "relationships": [], "properties": {}}

