from typing import Any, Dict, List, Literal, Optional

from app.services.llm_service import generate_cypher, summarize_answer
from app.services.neo4j_service import run_read_query
from app.services.schema_service import get_schema_description
from app.utils.cypher_guard import ensure_readonly_cypher
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()


class Message(BaseModel):
    role: Literal["user", "assistant"]
    content: str


class ChatRequest(BaseModel):
    message: str
    history: Optional[List[Message]] = None


class GraphData(BaseModel):
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]


class ChatResponse(BaseModel):
    answerText: str
    graph: GraphData
    query: Dict[str, Any]
    meta: Dict[str, Any]


@router.post("/", response_model=ChatResponse)
async def chat(req: ChatRequest):
    # 1) 取得/生成 schema 描述
    schema_desc = await get_schema_description()

    # 2) 讓 LLM 產生 Cypher（步驟一）
    history = req.history or []
    cypher_result = await generate_cypher(req.message, schema_desc, history)
    cypher = cypher_result.get("cypher", "")
    params = cypher_result.get("params", {})

    # 3) 安全檢查與 LIMIT 處理
    try:
        cypher = ensure_readonly_cypher(cypher)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

    # 4) 執行 Neo4j 查詢（只讀）
    records, result_count, duration_ms = await run_read_query(cypher, params)

    # 5) 轉為圖資料（針對 Neo4j 驅動的 Node/Relationship 物件）
    nodes: Dict[str, Dict[str, Any]] = {}
    edges: List[Dict[str, Any]] = []
    edge_ids_seen = set()  # 追蹤已見過的edge ID，避免重複



    for rec in records:
        for key, val in rec.items():
            # 更寬鬆的 Neo4j Node 檢測
            if hasattr(val, "id") and hasattr(val, "labels"):
                # Neo4j Node
                nid = str(val.id)
                labels = list(val.labels) if val.labels else ["Node"]
                ntype = labels[0]

                # 嘗試多種方式取得屬性
                props = {}
                if hasattr(val, "items"):
                    props = dict(val.items())
                elif hasattr(val, "__getitem__"):
                    # 嘗試直接當作字典存取
                    try:
                        props = dict(val)
                    except:
                        pass

                # 根據節點類型決定顯示標籤
                if ntype == "Case":
                    display_label = props.get("title", props.get("case_id", nid))
                elif ntype == "Person":
                    display_label = props.get("name", nid)
                elif ntype == "Event":
                    display_label = props.get("event_type", nid)
                else:
                    display_label = props.get("name", nid)

                nodes[nid] = {
                    "id": nid,
                    "label": display_label,
                    "type": ntype,
                    "properties": props,  # 保存完整屬性供前端點擊時顯示
                }

            # 更寬鬆的 Neo4j Relationship 檢測
            elif hasattr(val, "type"):
                start_id = None
                end_id = None

                # 嘗試多種方式取得起點終點
                if hasattr(val, "start_node") and hasattr(val, "end_node"):
                    start_id = str(val.start_node.id)
                    end_id = str(val.end_node.id)
                elif hasattr(val, "nodes"):
                    # 某些版本可能用 nodes 屬性
                    nodes_list = val.nodes
                    if len(nodes_list) >= 2:
                        start_id = str(nodes_list[0].id)
                        end_id = str(nodes_list[1].id)

                if start_id and end_id:
                    rid = f"{start_id}-{val.type}-{end_id}"
                    # 避免重複的edge ID
                    if rid not in edge_ids_seen:
                        edge_ids_seen.add(rid)
                        edges.append({
                            "id": rid,
                            "source": start_id,
                            "target": end_id,
                            "label": val.type,
                        })

    graph = GraphData(nodes=list(nodes.values()), edges=edges)

    # 6) 請 LLM 產出簡短答案（步驟二）
    answer_text = await summarize_answer(question=req.message, records=records, cypher=cypher)

    # 7) 回傳
    return ChatResponse(
        answerText=answer_text,
        graph=graph,
        query={"cypher": cypher, "params": params},
        meta={"durationMs": duration_ms, "model": "gpt-4o-mini", "resultCount": result_count},
    )

