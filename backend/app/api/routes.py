from fastapi import APIRouter

from app.api.v1.chat import router as chat_router
from app.api.v1.schema import router as schema_router
from app.api.v1.examples import router as examples_router

router = APIRouter()

router.include_router(chat_router, prefix="/chat", tags=["chat"])
router.include_router(schema_router, prefix="/schema", tags=["schema"])
router.include_router(examples_router, prefix="/examples", tags=["examples"])

