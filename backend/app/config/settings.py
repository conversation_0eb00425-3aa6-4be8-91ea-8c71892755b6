import os
from typing import List

from pydantic import BaseModel

# 自動載入 .env（若存在）
try:
    from dotenv import find_dotenv, load_dotenv
    load_dotenv(find_dotenv(), override=False)
except Exception:
    pass


class Settings(BaseModel):
    neo4j_uri: str = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user: str = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password: str = os.getenv("NEO4J_PASSWORD", "password")

    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    model_name: str = os.getenv("MODEL_NAME", "gpt-4o-mini")

    query_limit: int = int(os.getenv("QUERY_LIMIT", "100"))
    query_timeout_ms: int = int(os.getenv("QUERY_TIMEOUT_MS", "8000"))

    schema_introspect: bool = os.getenv("SCHEMA_INTROSPECT", "true").lower() == "true"
    schema_cache_path: str = os.getenv("SCHEMA_CACHE_PATH", "schema/schema.json")

    # 成本控制選項
    llm_heuristic_events: bool = os.getenv("LLM_HEURISTIC_EVENTS", "true").lower() == "true"
    llm_summary_rules: bool = os.getenv("LLM_SUMMARY_RULES", "true").lower() == "true"

    allowed_origins: List[str] = (
        os.getenv("ALLOWED_ORIGINS", "http://localhost:5173,http://localhost:3000")
        .split(",")
    )


_settings: Settings | None = None


def get_settings() -> Settings:
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings

