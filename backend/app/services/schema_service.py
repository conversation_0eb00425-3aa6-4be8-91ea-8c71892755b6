import json
from pathlib import Path
from typing import Any, Dict

from app.config.settings import get_settings
from app.utils.schema_parser import (introspect_schema,
                                     parse_schema_from_sources)

_schema_cache: Dict[str, Any] | None = None

# 解析相對路徑時的基準：
# 本檔案位於 backend/app/services/schema_service.py
# BACKEND_DIR = parents[2]，PROJECT_ROOT = parents[3]
BACKEND_DIR = Path(__file__).resolve().parents[2]
PROJECT_ROOT = Path(__file__).resolve().parents[3]


async def get_schema_description() -> Dict[str, Any]:
    global _schema_cache
    if _schema_cache is not None:
        return _schema_cache

    settings = get_settings()

    # 快取路徑：若傳入相對路徑，則以 BACKEND_DIR 為基準
    cache_path = Path(settings.schema_cache_path)
    if not cache_path.is_absolute():
        cache_path = BACKEND_DIR / cache_path

    if cache_path.exists():
        try:
            _schema_cache = json.loads(cache_path.read_text(encoding="utf-8"))
            return _schema_cache
        except Exception:
            pass

    # 重新解析：總是從專案根目錄下的 data-analysis 讀取
    desc = parse_schema_from_sources(
        cypher_dir=PROJECT_ROOT / "data-analysis/cypher_output",
        verdict_graph_path=PROJECT_ROOT / "data-analysis/verdict_graph.py",
    )

    if settings.schema_introspect:
        try:
            db_schema = introspect_schema()
            # 合併（簡單去重拼接即可；保持從簡）
            desc["labels"] = sorted(list({*desc.get("labels", []), *db_schema.get("labels", [])}))
            desc["relationships"] = sorted(list({*desc.get("relationships", []), *db_schema.get("relationships", [])}))
        except Exception:
            # 無權或失敗時忽略
            pass

    cache_path.parent.mkdir(parents=True, exist_ok=True)
    cache_path.write_text(json.dumps(desc, ensure_ascii=False, indent=2), encoding="utf-8")
    _schema_cache = desc
    return desc


async def refresh_schema_cache() -> None:
    global _schema_cache
    _schema_cache = None
    await get_schema_description()

