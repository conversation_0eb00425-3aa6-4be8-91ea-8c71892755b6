import time
from typing import Any, Dict, List, Tuple

from app.config.settings import get_settings
from neo4j import READ_ACCESS, GraphDatabase

_driver = None


def _get_driver():
    global _driver
    if _driver is None:
        s = get_settings()
        _driver = GraphDatabase.driver(s.neo4j_uri, auth=(s.neo4j_user, s.neo4j_password))
    return _driver


async def run_read_query(cypher: str, params: Dict[str, Any] | None = None) -> Tuple[List[Dict[str, Any]], int, int]:
    """
    執行只讀查詢，回傳 (records, result_count, duration_ms)
    records: 保留 Neo4j 原始物件，每筆包含 key->value (Node/Relationship/scalar)
    """
    driver = _get_driver()
    s = get_settings()

    def _work(tx):
        start = time.time()
        res = tx.run(cypher, **(params or {}))
        # 保留原始 Neo4j 物件，不要用 .data() 轉成字典
        records = []
        for r in res:
            record_dict = {}
            for key in r.keys():
                record_dict[key] = r[key]  # 保留原始 Node/Relationship 物件
            records.append(record_dict)
        duration_ms = int((time.time() - start) * 1000)
        return records, len(records), duration_ms

    with driver.session(default_access_mode=READ_ACCESS) as session:
        return session.execute_read(_work)

