import json
from typing import Any, Dict, List, Union

from app.config.settings import get_settings

try:
    from openai import AsyncOpenAI
except Exception:  # 開發期若未安裝 openai 套件，允許匯入失敗
    AsyncOpenAI = None  # type: ignore

_client: Any | None = None


def _get_client() -> Any | None:
    global _client
    if _client is not None:
        return _client
    s = get_settings()
    if AsyncOpenAI and s.openai_api_key:
        _client = AsyncOpenAI(api_key=s.openai_api_key)
    else:
        _client = None
    return _client


def _json_safe_records(records: List[Dict[str, Any]], limit: int = 20) -> List[Dict[str, Any]]:
    """將 neo4j-driver 回傳的記錄轉為可 JSON 化的結構。"""
    safe: List[Dict[str, Any]] = []
    for rec in records[:limit]:
        srec: Dict[str, Any] = {}
        for k, v in rec.items():
            try:
                # neo4j Node: 有 id/labels，可轉 dict(v) 取得屬性
                if hasattr(v, "id") and hasattr(v, "labels"):
                    srec[k] = {
                        "_type": "node",
                        "id": getattr(v, "id", None),
                        "labels": list(getattr(v, "labels", [])),
                        "props": dict(v),
                    }
                # neo4j Relationship: 有 type/start_node/end_node
                elif hasattr(v, "type") and (
                    hasattr(v, "start_node") or hasattr(v, "start_node_id") or hasattr(v, "startNodeId")
                ):
                    def _nid(x):
                        return getattr(x, "id", x)

                    start_id = None
                    end_id = None
                    for attr in ("start_node", "start_node_id", "startNodeId"):
                        if hasattr(v, attr):
                            start_id = _nid(getattr(v, attr))
                            break
                    for attr in ("end_node", "end_node_id", "endNodeId"):
                        if hasattr(v, attr):
                            end_id = _nid(getattr(v, attr))
                            break
                    srec[k] = {
                        "_type": "rel",
                        "type": getattr(v, "type", None),
                        "start": start_id,
                        "end": end_id,
                        "props": dict(v) if hasattr(v, "keys") else {},
                    }
                else:
                    # 標量或其他可序列化
                    json.dumps(v)  # 驗證可序列化
                    srec[k] = v
            except Exception:
                srec[k] = str(v)
        safe.append(srec)
    return safe


async def generate_cypher(question: str, schema_desc: Dict[str, Any], history: List[Any] = None) -> Dict[str, Any]:
    """
    呼叫 OpenAI 依據 schema 產生只讀 Cypher；若不可用則回退為保守查詢。
    期望輸出：{"cypher": "MATCH ... RETURN ... LIMIT 100", "params": {}, "intent": "..."}
    """
    s = get_settings()
    client = _get_client()

    if client is None:
        # 回退：保守查詢（根據實際資料結構：Case-[:HAS_PERSON]->Person）
        cypher = "MATCH (c:Case)-[:HAS_PERSON]->(p:Person) RETURN c, p LIMIT {limit}".replace("{limit}", str(s.query_limit))
        return {"cypher": cypher, "params": {}, "intent": "fallback"}

    system_msg = (
        "你是 Neo4j Cypher 專家和法律領域專家。根據用戶問題和提供的 schema 生成精確的只讀查詢。\n\n"
        "重要原則：\n"
        "1. 仔細分析 schema 中的節點標籤、關係類型和屬性\n"
        "2. 根據問題意圖選擇最相關的節點和關係路徑\n"
        "3. 使用 OPTIONAL MATCH 來獲取可能不存在的關係\n"
        "4. 對於模糊查詢使用 CONTAINS、STARTS WITH 等模糊匹配\n"
        "5. 必須包含 LIMIT 子句，預設不超過提供的 limit 值\n\n"
        "法律領域提示：\n"
        "- 案件(Case)通常是查詢的中心節點\n"
        "- 人物(Person)、組織(Organization)、地點(Location)、法條(Law)、事件(Event)都可能與案件相關\n"
        "- 注意同義詞：詐騙/詐欺、公司/組織等\n\n"
        "輸出格式：嚴格的 JSON 格式\n"
        "{\"cypher\": \"完整的 Cypher 查詢\", \"params\": {參數對象}, \"intent\": \"查詢意圖說明\"}"
    )

    # 構建更詳細的 schema 描述
    schema_context = {
        "nodes": [
            {"label": label, "properties": schema_desc.get("properties", {}).get(label, [])}
            for label in schema_desc.get("labels", [])
        ],
        "relationships": [
            {"type": rel, "description": f"連接不同節點的 {rel} 關係"}
            for rel in schema_desc.get("relationships", [])
        ],
        "common_patterns": [
            "Case 作為中心節點連接其他實體",
            "Person 可能有多種角色(role)：當事人、證人、法官等",
            "使用 title、name 等屬性進行文本搜索"
        ]
    }

    user_msg = {
        "question": question,
        "schema": schema_context,
        "query_constraints": {
            "max_limit": s.query_limit,
            "readonly_only": True,
            "required_clauses": ["RETURN", "LIMIT"]
        }
    }

    # 根據問題關鍵詞提供相關範例
    examples = []
    question_lower = question.lower()
    
    if any(word in question_lower for word in ["人", "當事人", "姓名"]):
        examples.append({
            "pattern": "查詢特定人物",
            "example": "MATCH (c:Case)-[:HAS_PERSON]->(p:Person) WHERE p.name CONTAINS '張' RETURN c, p LIMIT 10"
        })
    
    if any(word in question_lower for word in ["案件", "判決", "法院"]):
        examples.append({
            "pattern": "查詢案件資訊", 
            "example": "MATCH (c:Case) WHERE c.title CONTAINS '詐欺' RETURN c LIMIT 10"
        })
    
    if examples:
        user_msg["examples"] = examples

    # 構建消息歷史
    messages = [{"role": "system", "content": system_msg}]
    
    # 添加對話歷史（只包含用戶問題，避免傳送大量圖譜數據）
    if history:
        recent_history = history[-6:]  # 取最近6條消息（3輪對話）
        for msg in recent_history:
            # 只添加用戶問題，跳過assistant回復（避免傳送圖譜數據）
            if hasattr(msg, 'role') and hasattr(msg, 'content'):
                if msg.role == "user" and msg.content:
                    messages.append({
                        "role": msg.role,
                        "content": msg.content
                    })
            # 處理字典格式（備用）
            elif isinstance(msg, dict) and msg.get("role") == "user" and msg.get("content"):
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
    
    # 添加當前問題
    messages.append({"role": "user", "content": json.dumps(user_msg, ensure_ascii=False)})

    try:
        resp = await client.chat.completions.create(
            model=s.model_name,
            temperature=0.1,
            response_format={"type": "json_object"},
            messages=messages,
        )
        content = resp.choices[0].message.content or "{}"
        data = json.loads(content)
        # 最低防呆
        cypher = str(data.get("cypher", ""))
        params = data.get("params", {}) or {}
        intent = str(data.get("intent", ""))
        if not cypher:
            raise ValueError("empty cypher")
        # 若 RETURN 只有屬性（e.method 等）而沒有裸變數（e/p/n/r/l/law），則做一次後備改寫
        def _ensure_graph_return(q: str) -> str:
            q_clean = q.rstrip(" ;")
            up = q_clean.upper()
            if " RETURN " not in up:
                return q_clean + f" RETURN e LIMIT {s.query_limit}"
            # 抓出 RETURN 子句內容（到 LIMIT/ORDER/END）
            ret_part = q_clean[up.index(" RETURN ") + len(" RETURN "):]
            stop_idx = len(ret_part)
            for kw in [" LIMIT ", " ORDER BY ", " SKIP ", " WHERE "]:
                u = ret_part.upper()
                if kw in u:
                    stop_idx = min(stop_idx, u.index(kw))
            items = [x.strip() for x in ret_part[:stop_idx].split(",") if x.strip()]
            def is_bare_var(x: str) -> bool:
                # 裸變數：單一識別字，或 "e AS something" 類型
                x_up = x.upper()
                if "." in x or "(" in x_up or ")" in x_up:
                    return False
                # c、p、n、r、l、law
                base = x.split()[0]
                return base in {"c","p","n","r","l","law"}
            if any(is_bare_var(it) for it in items):
                return q_clean
            # 找可用變數（優先 c/p/n）
            import re as _re
            vars_found = _re.findall(r"\(([a-zA-Z]\w*):", q_clean)
            preferred = next((v for v in ["c","p","n","l","law","r"] if v in vars_found), vars_found[0] if vars_found else "c")
            prefix = q_clean[:up.index(" RETURN ")]
            # 保留 LIMIT 之後
            suffix = ret_part[stop_idx:]
            return f"{prefix} RETURN {preferred}{suffix if suffix else ' LIMIT ' + str(s.query_limit)}"

        cypher = _ensure_graph_return(cypher)
        return {"cypher": cypher, "params": params, "intent": intent}
    except Exception:
        cypher = "MATCH (c:Case)-[:HAS_PERSON]->(p:Person) RETURN c, p LIMIT {limit}".replace("{limit}", str(s.query_limit))
        return {"cypher": cypher, "params": {}, "intent": "fallback"}


async def summarize_answer(question: str, records: List[Dict[str, Any]], cypher: str) -> str:
    """呼叫 OpenAI 產出 2–6 句中文答案；失敗時回退為簡訊息。"""
    if not records:
        return "目前查無資料。可嘗試改寫問題，增加具體人物、案件或時間範圍。"

    s = get_settings()
    client = _get_client()
    if client is None:
        return f"找到 {len(records)} 筆相關資料，已於圖譜中顯示重點節點與關係。"

    system_msg = (
        "你是中文法律助理。以2-6句簡潔回答問題，必要時指出依據。若證據不足請保守陳述。"
        "特殊：問題含「列出/展示/顯示圖譜」時，直接回「已列出相關知識圖譜，請查看圖譜區域。」"
    )

    payload = {
        "question": question,
        "used_cypher": cypher,
        "sample_records": _json_safe_records(records, limit=20),
    }

    try:
        resp = await client.chat.completions.create(
            model=s.model_name,
            temperature=0.3,
            messages=[
                {"role": "system", "content": system_msg},
                {"role": "user", "content": json.dumps(payload, ensure_ascii=False)},
            ],
        )
        return resp.choices[0].message.content or ""
    except Exception:
        return f"找到 {len(records)} 筆相關資料，已於圖譜中顯示重點節點與關係。"
