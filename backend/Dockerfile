# 基於官方 Python 映像
FROM python:3.11-slim

# 環境參數
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# 工作目錄設在 /app/backend
WORKDIR /app/backend

# 安裝系統依賴（如有需要可在此擴充）
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 複製需求與安裝套件
COPY backend/requirements.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# 複製後端原始碼（保持路徑結構）
COPY backend ./
# 若需要讀取專案根的 data-analysis，將其映射成 volume（建議在 docker run 時掛載）

# 暴露 API 端口
EXPOSE 8000

# 預設啟動命令
# 注意：若要載入外部 .env，可在 docker run 時以 --env-file 指定 backend/.env 或掛載為 volume
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

