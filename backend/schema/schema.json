{"labels": ["Case", "Event", "Law", "Location", "Organization", "Person"], "relationships": ["BELONGS_TO", "CITES_LAW", "HAS_EVENT", "HAS_PARTICIPANT", "HAS_PERSON", "INVOLVES_LOCATION", "INVOLVES_ORGANIZATION", "IN_LOCATION", "TARGET"], "properties": {"Case": ["case_id", "case_number", "case_type", "case_year", "court", "judgment_date", "judgment_day", "judgment_month", "judgment_type", "judgment_year", "pdf_url", "title", "verdict_items"], "Person": ["gender", "is_anonymous", "name", "relationship_to_others", "role", "uid"], "Location": ["address", "name", "type"], "Law": ["article", "description", "law_name"], "Event": ["amount", "bank", "confession", "credibility", "date_time", "event_id", "event_type", "evidence_metrics", "method", "reason"], "Organization": ["name", "org_type"]}, "field_descriptions": {"Case.title": "案件標題，如'詐欺等'、'竊盜'、'毒品危害防制條例'等，用於查詢案件類型", "Case.case_type": "案件程序類型，如'簡'、'金訴'、'審金訴緝'等，表示審理程序", "Case.court": "審理法院名稱", "Person.name": "人物姓名", "Person.role": "人物在案件中的角色，如'被告'、'法官'、'檢察官'、'證人'等"}, "query_patterns": {"詐欺案件": "MATCH (c:Case) WHERE c.title CONTAINS '詐欺' RETURN c", "竊盜案件": "MATCH (c:Case) WHERE c.title CONTAINS '竊盜' RETURN c", "毒品案件": "MATCH (c:Case) WHERE c.title CONTAINS '毒品' RETURN c", "特定人物": "MATCH (c:Case)-[:HAS_PERSON]->(p:Person) WHERE p.name CONTAINS '姓名' RETURN c, p", "特定法院": "MATCH (c:Case) WHERE c.court CONTAINS '法院名' RETURN c"}}